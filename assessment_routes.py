from flask import Blueprint, jsonify, request
from user import db
from assessment import Project, LearningActivity, CareerGoal, HealthMetric, IndustryKnowledge, EthicsCheck
import jwt
import os
from functools import wraps
from datetime import datetime

assessment_bp = Blueprint('assessment', __name__)

def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'message': '未提供令牌'}), 401

        try:
            token = token.split(' ')[1]
            payload = jwt.decode(token, os.environ.get('SECRET_KEY', 'dev_key'), algorithms=['HS256'])
            current_user_id = payload['user_id']
        except jwt.ExpiredSignatureError:
            return jsonify({'message': '令牌已过期'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'message': '无效的令牌'}), 401

        return f(current_user_id, *args, **kwargs)

    return decorated

# 项目经验复盘API
@assessment_bp.route('/projects', methods=['GET'])
@token_required
def get_projects(current_user_id):
    projects = Project.query.filter_by(user_id=current_user_id).all()
    return jsonify([{
        'id': project.id,
        'title': project.title,
        'objective': project.objective,
        'tech_challenge': project.tech_challenge,
        'solution': project.solution,
        'outcome': project.outcome,
        'reflection': project.reflection,
        'start_date': project.start_date.isoformat() if project.start_date else None,
        'end_date': project.end_date.isoformat() if project.end_date else None,
        'created_at': project.created_at,
        'updated_at': project.updated_at
    } for project in projects])

@assessment_bp.route('/projects', methods=['POST'])
@token_required
def add_project(current_user_id):
    data = request.get_json()

    new_project = Project(
        user_id=current_user_id,
        title=data['title'],
        objective=data.get('objective', ''),
        tech_challenge=data.get('tech_challenge', ''),
        solution=data.get('solution', ''),
        outcome=data.get('outcome', ''),
        reflection=data.get('reflection', ''),
        start_date=datetime.strptime(data['start_date'], '%Y-%m-%d').date() if data.get('start_date') else None,
        end_date=datetime.strptime(data['end_date'], '%Y-%m-%d').date() if data.get('end_date') else None
    )

    db.session.add(new_project)
    db.session.commit()

    return jsonify({'message': '项目添加成功'}), 201

@assessment_bp.route('/projects/<int:project_id>', methods=['PUT'])
@token_required
def update_project(current_user_id, project_id):
    project = Project.query.filter_by(id=project_id, user_id=current_user_id).first()
    if not project:
        return jsonify({'message': '项目不存在'}), 404

    data = request.get_json()
    project.title = data.get('title', project.title)
    project.objective = data.get('objective', project.objective)
    project.tech_challenge = data.get('tech_challenge', project.tech_challenge)
    project.solution = data.get('solution', project.solution)
    project.outcome = data.get('outcome', project.outcome)
    project.reflection = data.get('reflection', project.reflection)

    if data.get('start_date'):
        project.start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()
    if data.get('end_date'):
        project.end_date = datetime.strptime(data['end_date'], '%Y-%m-%d').date()

    db.session.commit()

    return jsonify({'message': '项目更新成功'})

@assessment_bp.route('/projects/<int:project_id>', methods=['DELETE'])
@token_required
def delete_project(current_user_id, project_id):
    project = Project.query.filter_by(id=project_id, user_id=current_user_id).first()
    if not project:
        return jsonify({'message': '项目不存在'}), 404

    db.session.delete(project)
    db.session.commit()

    return jsonify({'message': '项目删除成功'})

# 学习活动API
@assessment_bp.route('/learning', methods=['GET'])
@token_required
def get_learning_activities(current_user_id):
    activities = LearningActivity.query.filter_by(user_id=current_user_id).all()
    return jsonify([{
        'id': activity.id,
        'activity_type': activity.activity_type,
        'title': activity.title,
        'hours_spent': activity.hours_spent,
        'knowledge_repo_link': activity.knowledge_repo_link,
        'completion_status': activity.completion_status,
        'notes': activity.notes,
        'date': activity.date.isoformat() if activity.date else None,
        'created_at': activity.created_at,
        'updated_at': activity.updated_at
    } for activity in activities])

@assessment_bp.route('/learning', methods=['POST'])
@token_required
def add_learning_activity(current_user_id):
    data = request.get_json()

    new_activity = LearningActivity(
        user_id=current_user_id,
        activity_type=data['activity_type'],
        title=data['title'],
        hours_spent=data.get('hours_spent', 0),
        knowledge_repo_link=data.get('knowledge_repo_link', ''),
        completion_status=data.get('completion_status', '进行中'),
        notes=data.get('notes', ''),
        date=datetime.strptime(data['date'], '%Y-%m-%d').date() if data.get('date') else datetime.utcnow().date()
    )

    db.session.add(new_activity)
    db.session.commit()

    return jsonify({'message': '学习活动添加成功'}), 201

# 职业目标API
@assessment_bp.route('/career-goals', methods=['GET'])
@token_required
def get_career_goals(current_user_id):
    goals = CareerGoal.query.filter_by(user_id=current_user_id).all()
    return jsonify([{
        'id': goal.id,
        'title': goal.title,
        'goal_type': goal.goal_type,
        'description': goal.description,
        'progress': goal.progress,
        'resource_gap': goal.resource_gap,
        'target_date': goal.target_date.isoformat() if goal.target_date else None,
        'created_at': goal.created_at,
        'updated_at': goal.updated_at
    } for goal in goals])

@assessment_bp.route('/career-goals', methods=['POST'])
@token_required
def add_career_goal(current_user_id):
    data = request.get_json()

    new_goal = CareerGoal(
        user_id=current_user_id,
        title=data['title'],
        goal_type=data['goal_type'],
        description=data.get('description', ''),
        progress=data.get('progress', 0),
        resource_gap=data.get('resource_gap', ''),
        target_date=datetime.strptime(data['target_date'], '%Y-%m-%d').date() if data.get('target_date') else None
    )

    db.session.add(new_goal)
    db.session.commit()

    return jsonify({'message': '职业目标添加成功'}), 201

@assessment_bp.route('/career-goals/<int:goal_id>/progress', methods=['PUT'])
@token_required
def update_goal_progress(current_user_id, goal_id):
    goal = CareerGoal.query.filter_by(id=goal_id, user_id=current_user_id).first()
    if not goal:
        return jsonify({'message': '目标不存在'}), 404

    data = request.get_json()
    goal.progress = data.get('progress', goal.progress)

    db.session.commit()

    return jsonify({'message': '目标进度更新成功'})

# 行业知识匹配度API
@assessment_bp.route('/industry-knowledge', methods=['GET'])
@token_required
def get_industry_knowledge(current_user_id):
    knowledge = IndustryKnowledge.query.filter_by(user_id=current_user_id).all()
    return jsonify([{
        'id': item.id,
        'industry': item.industry,
        'dimension': item.dimension,
        'question': item.question,
        'status': item.status,
        'created_at': item.created_at,
        'updated_at': item.updated_at
    } for item in knowledge])

@assessment_bp.route('/industry-knowledge', methods=['POST'])
@token_required
def add_industry_knowledge(current_user_id):
    data = request.get_json()

    new_knowledge = IndustryKnowledge(
        user_id=current_user_id,
        industry=data['industry'],
        dimension=data['dimension'],
        question=data['question'],
        status=data.get('status', '')
    )

    db.session.add(new_knowledge)
    db.session.commit()

    return jsonify({'message': '行业知识项添加成功'}), 201

@assessment_bp.route('/industry-knowledge/<int:knowledge_id>', methods=['PUT'])
@token_required
def update_industry_knowledge(current_user_id, knowledge_id):
    knowledge = IndustryKnowledge.query.filter_by(id=knowledge_id, user_id=current_user_id).first()
    if not knowledge:
        return jsonify({'message': '知识项不存在'}), 404

    data = request.get_json()
    knowledge.industry = data.get('industry', knowledge.industry)
    knowledge.dimension = data.get('dimension', knowledge.dimension)
    knowledge.question = data.get('question', knowledge.question)
    knowledge.status = data.get('status', knowledge.status)

    db.session.commit()

    return jsonify({'message': '行业知识项更新成功'})

# 伦理与合规自检API
@assessment_bp.route('/ethics-checks', methods=['GET'])
@token_required
def get_ethics_checks(current_user_id):
    checks = EthicsCheck.query.filter_by(user_id=current_user_id).all()
    return jsonify([{
        'id': check.id,
        'check_item': check.check_item,
        'status': check.status,
        'practice_case': check.practice_case,
        'created_at': check.created_at,
        'updated_at': check.updated_at
    } for check in checks])

@assessment_bp.route('/ethics-checks', methods=['POST'])
@token_required
def add_ethics_check(current_user_id):
    data = request.get_json()

    new_check = EthicsCheck(
        user_id=current_user_id,
        check_item=data['check_item'],
        status=data.get('status', False),
        practice_case=data.get('practice_case', '')
    )

    db.session.add(new_check)
    db.session.commit()

    return jsonify({'message': '伦理检查项添加成功'}), 201

@assessment_bp.route('/ethics-checks/<int:check_id>', methods=['PUT'])
@token_required
def update_ethics_check(current_user_id, check_id):
    check = EthicsCheck.query.filter_by(id=check_id, user_id=current_user_id).first()
    if not check:
        return jsonify({'message': '检查项不存在'}), 404

    data = request.get_json()
    check.check_item = data.get('check_item', check.check_item)
    check.status = data.get('status', check.status)
    check.practice_case = data.get('practice_case', check.practice_case)

    db.session.commit()

    return jsonify({'message': '伦理检查项更新成功'})

# 健康与动力评估API
@assessment_bp.route('/health-metrics', methods=['GET'])
@token_required
def get_health_metrics(current_user_id):
    metrics = HealthMetric.query.filter_by(user_id=current_user_id).all()
    return jsonify([{
        'id': metric.id,
        'metric_type': metric.metric_type,
        'value': metric.value,
        'description': metric.description,
        'date': metric.date.isoformat() if metric.date else None,
        'created_at': metric.created_at,
        'updated_at': metric.updated_at
    } for metric in metrics])

@assessment_bp.route('/health-metrics', methods=['POST'])
@token_required
def add_health_metric(current_user_id):
    data = request.get_json()

    new_metric = HealthMetric(
        user_id=current_user_id,
        metric_type=data['metric_type'],
        value=data.get('value', 0),
        description=data.get('description', ''),
        date=datetime.strptime(data['date'], '%Y-%m-%d').date() if data.get('date') else datetime.utcnow().date()
    )

    db.session.add(new_metric)
    db.session.commit()

    return jsonify({'message': '健康指标添加成功'}), 201

@assessment_bp.route('/health-metrics/<int:metric_id>', methods=['PUT'])
@token_required
def update_health_metric(current_user_id, metric_id):
    metric = HealthMetric.query.filter_by(id=metric_id, user_id=current_user_id).first()
    if not metric:
        return jsonify({'message': '健康指标不存在'}), 404

    data = request.get_json()
    metric.metric_type = data.get('metric_type', metric.metric_type)
    metric.value = data.get('value', metric.value)
    metric.description = data.get('description', metric.description)

    if data.get('date'):
        metric.date = datetime.strptime(data['date'], '%Y-%m-%d').date()

    db.session.commit()

    return jsonify({'message': '健康指标更新成功'})

# 综合Dashboard数据API
@assessment_bp.route('/dashboard', methods=['GET'])
@token_required
def get_dashboard_data(current_user_id):
    """获取dashboard所需的综合数据"""
    from assessment import TechSkill, SoftSkill

    # 获取技术技能数据
    tech_skills = TechSkill.query.filter_by(user_id=current_user_id).all()
    tech_skills_data = []
    for skill in tech_skills:
        tech_skills_data.append({
            'subject': skill.name,
            'score': skill.score,
            'fullMark': 5
        })

    # 获取软技能数据
    soft_skills = SoftSkill.query.filter_by(user_id=current_user_id).all()
    soft_skills_data = []
    for skill in soft_skills:
        soft_skills_data.append({
            'subject': skill.name,
            'score': skill.score,
            'fullMark': 5
        })

    # 获取职业目标数据
    goals = CareerGoal.query.filter_by(user_id=current_user_id).all()
    goals_data = []
    for goal in goals:
        goals_data.append({
            'title': goal.title,
            'current': goal.progress,
            'total': 100,
            'dueDate': goal.target_date.isoformat() if goal.target_date else None,
            'category': goal.goal_type
        })

    # 获取最近的学习活动
    recent_activities = LearningActivity.query.filter_by(user_id=current_user_id)\
        .order_by(LearningActivity.date.desc()).limit(5).all()
    activities_data = []
    for activity in recent_activities:
        activities_data.append({
            'title': activity.title,
            'type': activity.activity_type,
            'date': activity.date.isoformat() if activity.date else None,
            'hours': activity.hours_spent,
            'status': activity.completion_status
        })

    # 获取健康指标概览
    health_metrics = HealthMetric.query.filter_by(user_id=current_user_id)\
        .order_by(HealthMetric.date.desc()).limit(10).all()
    health_data = []
    for metric in health_metrics:
        health_data.append({
            'type': metric.metric_type,
            'value': metric.value,
            'date': metric.date.isoformat() if metric.date else None
        })

    return jsonify({
        'tech_skills': tech_skills_data,
        'soft_skills': soft_skills_data,
        'goals': goals_data,
        'recent_activities': activities_data,
        'health_metrics': health_data,
        'summary': {
            'total_projects': Project.query.filter_by(user_id=current_user_id).count(),
            'total_learning_hours': db.session.query(db.func.sum(LearningActivity.hours_spent))\
                .filter_by(user_id=current_user_id).scalar() or 0,
            'completed_goals': CareerGoal.query.filter_by(user_id=current_user_id)\
                .filter(CareerGoal.progress >= 100).count(),
            'ethics_compliance': EthicsCheck.query.filter_by(user_id=current_user_id, status=True).count()
        }
    })