#!/usr/bin/env python3
"""
多职业功能API测试脚本
"""

import requests
import json
import sys

BASE_URL = "http://localhost:5002"

def test_profession_templates():
    """测试获取职业模板"""
    print("🧪 测试获取职业模板...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/profession/templates")
        if response.status_code == 200:
            data = response.json()
            templates = data.get('templates', [])
            print(f"✅ 成功获取 {len(templates)} 个职业模板:")
            for template in templates:
                print(f"   - {template['display_name']} ({template['name']})")
            return True
        else:
            print(f"❌ 获取职业模板失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_profession_detail(profession_name):
    """测试获取职业详情"""
    print(f"🧪 测试获取职业详情: {profession_name}")
    
    try:
        response = requests.get(f"{BASE_URL}/api/profession/templates/{profession_name}")
        if response.status_code == 200:
            data = response.json()
            template = data.get('template', {})
            config = template.get('config', {})
            
            print(f"✅ 成功获取 {template.get('display_name')} 详情:")
            print(f"   - 技术技能分类: {len(config.get('tech_skill_categories', []))} 个")
            print(f"   - 软技能: {len(config.get('soft_skills', []))} 个")
            print(f"   - 应用行业: {len(config.get('industries', []))} 个")
            print(f"   - 伦理检查: {len(config.get('ethics_checks', []))} 个")
            return True
        else:
            print(f"❌ 获取职业详情失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_skill_categories(profession_name):
    """测试获取技能分类"""
    print(f"🧪 测试获取技能分类: {profession_name}")
    
    try:
        response = requests.get(f"{BASE_URL}/api/profession/skill-categories/{profession_name}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 成功获取 {profession_name} 技能分类:")
            print(f"   - 技术技能分类: {len(data.get('tech_skill_categories', []))} 个")
            print(f"   - 软技能: {len(data.get('soft_skills', []))} 个")
            return True
        else:
            print(f"❌ 获取技能分类失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_dashboard_config(profession_name):
    """测试获取仪表盘配置"""
    print(f"🧪 测试获取仪表盘配置: {profession_name}")
    
    try:
        response = requests.get(f"{BASE_URL}/api/profession/dashboard-config/{profession_name}")
        if response.status_code == 200:
            data = response.json()
            config = data.get('config', {})
            print(f"✅ 成功获取 {profession_name} 仪表盘配置:")
            print(f"   - 标题: {config.get('title')}")
            print(f"   - 模块数量: {len(config.get('modules', []))} 个")
            return True
        else:
            print(f"❌ 获取仪表盘配置失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_user_profession_without_auth():
    """测试未认证情况下获取用户职业信息"""
    print("🧪 测试未认证获取用户职业信息...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/profession/user-profession")
        if response.status_code == 401:
            print("✅ 正确返回401未认证错误")
            return True
        else:
            print(f"❌ 期望401错误，实际返回: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def main():
    """主测试流程"""
    print("🚀 开始多职业功能API测试...")
    print("=" * 60)
    
    test_results = []
    
    # 测试基础API
    test_results.append(test_profession_templates())
    
    # 测试各个职业的详情
    professions = [
        'ai_engineer',
        'frontend_developer', 
        'backend_developer',
        'product_manager',
        'data_scientist',
        'ui_ux_designer'
    ]
    
    for profession in professions:
        test_results.append(test_profession_detail(profession))
        test_results.append(test_skill_categories(profession))
        test_results.append(test_dashboard_config(profession))
        print()  # 空行分隔
    
    # 测试认证相关API
    test_results.append(test_user_profession_without_auth())
    
    # 统计结果
    print("=" * 60)
    print("📊 测试结果统计:")
    passed = sum(test_results)
    total = len(test_results)
    print(f"✅ 通过: {passed}/{total}")
    print(f"❌ 失败: {total - passed}/{total}")
    print(f"📈 成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！多职业功能API正常工作")
        return 0
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查相关功能")
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
