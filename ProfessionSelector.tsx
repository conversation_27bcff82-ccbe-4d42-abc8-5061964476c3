import React, { useState, useEffect } from 'react';
import { User, Briefcase, Settings, Check } from 'lucide-react';
import { professionAPI } from './src/api';

interface ProfessionTemplate {
  name: string;
  display_name: string;
  description: string;
}

interface ProfessionSelectorProps {
  currentProfession?: string;
  onProfessionChange?: (profession: string) => void;
  showDescription?: boolean;
}

const ProfessionSelector: React.FC<ProfessionSelectorProps> = ({
  currentProfession = 'ai_engineer',
  onProfessionChange,
  showDescription = true
}) => {
  const [professions, setProfessions] = useState<ProfessionTemplate[]>([]);
  const [selectedProfession, setSelectedProfession] = useState(currentProfession);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadProfessions();
  }, []);

  const loadProfessions = async () => {
    try {
      const response = await professionAPI.getTemplates();
      setProfessions(response.data.templates);
    } catch (error) {
      console.error('加载职业模板失败:', error);
      setError('加载职业模板失败');
      // 使用默认数据
      setProfessions([
        {
          name: 'ai_engineer',
          display_name: 'AI应用工程师',
          description: '专注于AI应用开发、模型部署和智能系统构建的工程师'
        },
        {
          name: 'frontend_developer',
          display_name: '前端开发工程师',
          description: '专注于用户界面开发和用户体验优化的工程师'
        },
        {
          name: 'backend_developer',
          display_name: '后端开发工程师',
          description: '专注于服务器端开发和系统架构设计的工程师'
        },
        {
          name: 'product_manager',
          display_name: '产品经理',
          description: '负责产品规划、设计和管理的专业人员'
        },
        {
          name: 'data_scientist',
          display_name: '数据科学家',
          description: '专注于数据分析、机器学习和商业洞察的专业人员'
        },
        {
          name: 'ui_ux_designer',
          display_name: 'UI/UX设计师',
          description: '专注于用户界面和用户体验设计的专业人员'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleProfessionSelect = (professionName: string) => {
    setSelectedProfession(professionName);
    if (onProfessionChange) {
      onProfessionChange(professionName);
    }
  };

  const getProfessionIcon = (professionName: string) => {
    const iconMap: { [key: string]: React.ReactNode } = {
      'ai_engineer': <Briefcase className="h-6 w-6" />,
      'frontend_developer': <User className="h-6 w-6" />,
      'backend_developer': <Settings className="h-6 w-6" />,
      'product_manager': <Briefcase className="h-6 w-6" />,
      'data_scientist': <Settings className="h-6 w-6" />,
      'ui_ux_designer': <User className="h-6 w-6" />
    };
    return iconMap[professionName] || <Briefcase className="h-6 w-6" />;
  };

  const getProfessionColor = (professionName: string) => {
    const colorMap: { [key: string]: string } = {
      'ai_engineer': 'blue',
      'frontend_developer': 'green',
      'backend_developer': 'purple',
      'product_manager': 'orange',
      'data_scientist': 'red',
      'ui_ux_designer': 'pink'
    };
    return colorMap[professionName] || 'blue';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-8">
        <p className="text-red-500">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {professions.map((profession) => {
          const isSelected = selectedProfession === profession.name;
          const color = getProfessionColor(profession.name);

          return (
            <div
              key={profession.name}
              className={`relative p-6 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                isSelected
                  ? `border-${color}-500 bg-${color}-50 dark:bg-${color}-900/20`
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
              onClick={() => handleProfessionSelect(profession.name)}
            >
              {isSelected && (
                <div className={`absolute top-2 right-2 text-${color}-500`}>
                  <Check className="h-5 w-5" />
                </div>
              )}

              <div className="flex items-center space-x-3 mb-3">
                <div className={`text-${color}-500`}>
                  {getProfessionIcon(profession.name)}
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  {profession.display_name}
                </h3>
              </div>

              {showDescription && (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {profession.description}
                </p>
              )}
            </div>
          );
        })}
      </div>

      {selectedProfession && (
        <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div className="flex items-center space-x-2">
            <Check className="h-5 w-5 text-blue-500" />
            <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
              已选择: {professions.find(p => p.name === selectedProfession)?.display_name}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfessionSelector;
