import { useState, useEffect } from 'react';
import MainLayout from './MainLayout';
import SkillRadar<PERSON>hart from './SkillRadarChart';
import ProgressCard from './ProgressCard';
import { assessmentAPI, professionAPI } from './src/api';
import { Clock, Calendar, BookOpen, Award, Target, TrendingUp, Users, Shield, Heart, Settings } from 'lucide-react';

interface DashboardData {
  tech_skills: Array<{ subject: string; score: number; fullMark: number }>;
  soft_skills: Array<{ subject: string; score: number; fullMark: number }>;
  goals: Array<{ title: string; current: number; total: number; dueDate: string; category: string }>;
  recent_activities: Array<{ title: string; type: string; date: string; hours?: number; status?: string }>;
  health_metrics: Array<{ type: string; value: number; date: string }>;
  summary: {
    total_projects: number;
    total_learning_hours: number;
    completed_goals: number;
    ethics_compliance: number;
  };
}

const Dashboard = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [userProfession, setUserProfession] = useState<any>(null);
  const [dashboardConfig, setDashboardConfig] = useState<any>(null);

  useEffect(() => {
    loadUserProfession();
    loadDashboardData();
  }, []);

  const loadUserProfession = async () => {
    try {
      const response = await professionAPI.getUserProfession();
      setUserProfession(response.data);

      // 加载仪表盘配置
      const configResponse = await professionAPI.getDashboardConfig(response.data.profession_type);
      setDashboardConfig(configResponse.data.config);
    } catch (error) {
      console.error('加载用户职业信息失败:', error);
      // 使用默认配置
      setDashboardConfig({
        title: 'AI应用开发者活动仪表盘',
        description: '全面跟踪您的技能发展、项目进度和学习路径'
      });
    }
  };

  const loadDashboardData = async () => {
    try {
      const response = await assessmentAPI.getDashboardData();
      setDashboardData(response.data);
    } catch (error) {
      console.error('加载仪表盘数据失败:', error);
      // 使用默认数据
      setDashboardData({
        tech_skills: [
          { subject: 'Python高级特性', score: 4, fullMark: 5 },
          { subject: 'SQL复杂查询', score: 3.5, fullMark: 5 },
          { subject: 'PyTorch', score: 3, fullMark: 5 },
          { subject: 'Transformer', score: 2.5, fullMark: 5 },
          { subject: '数据处理', score: 4.5, fullMark: 5 },
          { subject: '模型部署', score: 3, fullMark: 5 },
        ],
        soft_skills: [
          { subject: '沟通表达', score: 4, fullMark: 5 },
          { subject: '团队协作', score: 4.5, fullMark: 5 },
          { subject: '时间管理', score: 3, fullMark: 5 },
          { subject: '压力应对', score: 3.5, fullMark: 5 },
          { subject: '创新思维', score: 4, fullMark: 5 },
        ],
        goals: [
          { title: "掌握LangChain智能体开发", current: 3, total: 5, dueDate: "2025-06-30", category: "短期目标" },
          { title: "完成5个AI应用项目复盘", current: 2, total: 5, dueDate: "2025-07-15", category: "学习任务" },
          { title: "提升数据处理效率技能", current: 4, total: 10, dueDate: "2025-08-01", category: "能力提升" }
        ],
        recent_activities: [
          { title: "完成了《LangChain高级应用》课程", type: "课程", date: "2024-01-15", hours: 3, status: "已完成" },
          { title: "更新了Python高级特性技能评分", type: "技能评估", date: "2024-01-14", status: "已完成" },
          { title: "添加了新目标：掌握LangChain智能体开发", type: "目标设定", date: "2024-01-13", status: "进行中" }
        ],
        health_metrics: [
          { type: "工作节奏", value: 7, date: "2024-01-15" },
          { type: "学习倦怠", value: 3, date: "2024-01-15" },
          { type: "动力水平", value: 8, date: "2024-01-15" }
        ],
        summary: {
          total_projects: 5,
          total_learning_hours: 120,
          completed_goals: 3,
          ethics_compliance: 8
        }
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-400">加载中...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (!dashboardData) {
    return (
      <MainLayout>
        <div className="text-center py-12">
          <p className="text-gray-500 dark:text-gray-400">暂无数据</p>
        </div>
      </MainLayout>
    );
  }

  const averageHealthScore = dashboardData.health_metrics.length > 0
    ? dashboardData.health_metrics.reduce((sum, metric) => sum + metric.value, 0) / dashboardData.health_metrics.length
    : 0;

  return (
    <MainLayout>
      <div className="p-6">
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardConfig?.title || 'AI应用开发者活动仪表盘'}
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                {dashboardConfig?.description || '全面跟踪您的技能发展、项目进度和学习路径'}
              </p>
            </div>
            <div className="flex items-center space-x-2">
              {userProfession && (
                <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 text-sm rounded-full">
                  {userProfession.template?.display_name || '职业类型'}
                </span>
              )}
              <button
                onClick={() => window.location.href = '/settings'}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                title="设置"
              >
                <Settings className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>

        {/* 8大能力概览卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">学习时间</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{dashboardData.summary.total_learning_hours}小时</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <BookOpen className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">项目经验</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{dashboardData.summary.total_projects}个</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <Target className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">完成目标</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{dashboardData.summary.completed_goals}个</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <Heart className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">健康指数</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{averageHealthScore.toFixed(1)}/10</p>
              </div>
            </div>
          </div>
        </div>

        {/* 8大能力详细概览 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">技术能力</h3>
              <Award className="h-6 w-6 text-blue-600" />
            </div>
            <p className="text-2xl font-bold text-blue-600">{dashboardData.tech_skills.length}</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">项技能</p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">软技能</h3>
              <Users className="h-6 w-6 text-green-600" />
            </div>
            <p className="text-2xl font-bold text-green-600">{dashboardData.soft_skills.length}</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">项技能</p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">伦理合规</h3>
              <Shield className="h-6 w-6 text-purple-600" />
            </div>
            <p className="text-2xl font-bold text-purple-600">{dashboardData.summary.ethics_compliance}</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">项检查</p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">学习活动</h3>
              <BookOpen className="h-6 w-6 text-yellow-600" />
            </div>
            <p className="text-2xl font-bold text-yellow-600">{dashboardData.recent_activities.length}</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">项活动</p>
          </div>
        </div>

        {/* 主要内容区 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          {/* 左侧：技能雷达图 */}
          <div className="lg:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
            {dashboardData.tech_skills.length > 0 && (
              <SkillRadarChart data={dashboardData.tech_skills} title="技术能力评估" />
            )}
            {dashboardData.soft_skills.length > 0 && (
              <SkillRadarChart data={dashboardData.soft_skills} title="软技能评估" />
            )}
          </div>

          {/* 右侧：目标进度卡片 */}
          <div className="space-y-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">目标追踪</h2>
            {dashboardData.goals.map((goal, index) => (
              <ProgressCard
                key={index}
                title={goal.title}
                current={goal.current}
                total={goal.total}
                dueDate={goal.dueDate}
                category={goal.category}
              />
            ))}
            {dashboardData.goals.length === 0 && (
              <div className="text-center py-8">
                <Target className="mx-auto h-12 w-12 text-gray-400" />
                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">暂无目标</p>
              </div>
            )}
          </div>
        </div>

        {/* 底部区域：最近活动 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">最近活动</h3>
          </div>
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {dashboardData.recent_activities.map((activity, index) => (
              <div key={index} className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    {activity.type === '课程' && <BookOpen className="h-5 w-5 text-blue-600" />}
                    {activity.type === '技能评估' && <Award className="h-5 w-5 text-green-600" />}
                    {activity.type === '目标设定' && <Target className="h-5 w-5 text-purple-600" />}
                    {activity.type === '项目' && <BookOpen className="h-5 w-5 text-orange-600" />}
                  </div>
                  <div className="ml-4 flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {activity.title}
                    </p>
                    <div className="flex items-center space-x-4 mt-1">
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(activity.date).toLocaleDateString('zh-CN')}
                      </p>
                      {activity.hours && (
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {activity.hours}小时
                        </p>
                      )}
                      {activity.status && (
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          activity.status === '已完成' ? 'bg-green-100 text-green-800' :
                          activity.status === '进行中' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {activity.status}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
            {dashboardData.recent_activities.length === 0 && (
              <div className="text-center py-12">
                <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">暂无最近活动</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  开始添加学习活动和目标
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default Dashboard;
