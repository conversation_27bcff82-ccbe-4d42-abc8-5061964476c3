import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowRight, CheckCircle, Info, Sparkles } from 'lucide-react';
import ProfessionSelector from './ProfessionSelector';
import axios from 'axios';

interface ProfessionSetupProps {
  isFirstTime?: boolean;
  currentProfession?: string;
}

const ProfessionSetup: React.FC<ProfessionSetupProps> = ({ 
  isFirstTime = false,
  currentProfession = 'ai_engineer'
}) => {
  const navigate = useNavigate();
  const [selectedProfession, setSelectedProfession] = useState(currentProfession);
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(1);
  const [professionDetails, setProfessionDetails] = useState<any>(null);

  useEffect(() => {
    if (selectedProfession) {
      loadProfessionDetails(selectedProfession);
    }
  }, [selectedProfession]);

  const loadProfessionDetails = async (professionName: string) => {
    try {
      const response = await axios.get(`/api/profession/templates/${professionName}`);
      setProfessionDetails(response.data.template);
    } catch (error) {
      console.error('加载职业详情失败:', error);
    }
  };

  const handleProfessionChange = (profession: string) => {
    setSelectedProfession(profession);
    setStep(2);
  };

  const handleConfirm = async () => {
    setLoading(true);
    try {
      // 更新用户职业类型
      await axios.put('/api/profession/user-profession', {
        profession_type: selectedProfession
      }, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      // 初始化职业相关数据
      await axios.post('/api/profession/initialize-user-data', {
        profession_type: selectedProfession
      }, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      // 跳转到仪表盘
      navigate('/dashboard');
    } catch (error) {
      console.error('设置职业类型失败:', error);
      alert('设置失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      <div className="flex items-center space-x-4">
        <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
          step >= 1 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'
        }`}>
          {step > 1 ? <CheckCircle className="h-5 w-5" /> : '1'}
        </div>
        <div className={`h-1 w-16 ${step >= 2 ? 'bg-blue-600' : 'bg-gray-200'}`}></div>
        <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
          step >= 2 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'
        }`}>
          {step > 2 ? <CheckCircle className="h-5 w-5" /> : '2'}
        </div>
      </div>
    </div>
  );

  const renderProfessionOverview = () => {
    if (!professionDetails) return null;

    const config = professionDetails.config;
    
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div className="text-center mb-6">
          <Sparkles className="h-12 w-12 text-blue-600 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            {professionDetails.display_name}
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            {professionDetails.description}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 技术技能分类 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <Info className="h-5 w-5 text-blue-600 mr-2" />
              核心技术领域
            </h3>
            <div className="space-y-2">
              {config.tech_skill_categories?.slice(0, 5).map((category: any, index: number) => (
                <div key={index} className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    {category.display_name}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* 软技能 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <Info className="h-5 w-5 text-green-600 mr-2" />
              关键软技能
            </h3>
            <div className="space-y-2">
              {config.soft_skills?.slice(0, 5).map((skill: string, index: number) => (
                <div key={index} className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    {skill}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* 应用行业 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <Info className="h-5 w-5 text-purple-600 mr-2" />
              应用行业
            </h3>
            <div className="flex flex-wrap gap-2">
              {config.industries?.slice(0, 6).map((industry: string, index: number) => (
                <span key={index} className="px-2 py-1 bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 text-xs rounded-full">
                  {industry}
                </span>
              ))}
            </div>
          </div>

          {/* 伦理关注点 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <Info className="h-5 w-5 text-orange-600 mr-2" />
              伦理关注点
            </h3>
            <div className="space-y-2">
              {config.ethics_checks?.slice(0, 4).map((check: string, index: number) => (
                <div key={index} className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-orange-600 rounded-full"></div>
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    {check}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div className="flex items-start space-x-3">
            <Info className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                选择此职业类型后，您将获得：
              </h4>
              <ul className="mt-2 text-sm text-blue-700 dark:text-blue-200 space-y-1">
                <li>• 专门针对{professionDetails.display_name}的技能评估框架</li>
                <li>• 行业相关的项目经验模板和案例</li>
                <li>• 个性化的学习路径和职业发展建议</li>
                <li>• 专业的能力仪表盘和数据分析</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* 头部 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            {isFirstTime ? '欢迎使用通用职业能力管理平台' : '更改职业类型'}
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400">
            {isFirstTime 
              ? '请选择您的职业类型，我们将为您提供专业的能力评估和发展建议'
              : '选择新的职业类型以获得相应的评估框架和功能'
            }
          </p>
        </div>

        {/* 步骤指示器 */}
        {renderStepIndicator()}

        {/* 内容区域 */}
        <div className="space-y-8">
          {step === 1 && (
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 text-center">
                选择您的职业类型
              </h2>
              <ProfessionSelector
                currentProfession={selectedProfession}
                onProfessionChange={handleProfessionChange}
                showDescription={true}
              />
            </div>
          )}

          {step === 2 && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white text-center">
                确认您的选择
              </h2>
              {renderProfessionOverview()}
              
              <div className="flex justify-center space-x-4">
                <button
                  onClick={() => setStep(1)}
                  className="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  返回选择
                </button>
                <button
                  onClick={handleConfirm}
                  disabled={loading}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>设置中...</span>
                    </>
                  ) : (
                    <>
                      <span>确认选择</span>
                      <ArrowRight className="h-4 w-4" />
                    </>
                  )}
                </button>
              </div>
            </div>
          )}
        </div>

        {/* 底部提示 */}
        <div className="mt-12 text-center">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            您可以随时在设置中更改职业类型
          </p>
        </div>
      </div>
    </div>
  );
};

export default ProfessionSetup;
