#!/usr/bin/env python3
"""
数据库架构迁移脚本：添加通用化所需的新字段
"""

import sys
import os
import sqlite3
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def get_db_path():
    """获取数据库文件路径"""
    return os.path.join(os.path.dirname(__file__), 'instance', 'ai_dev_dashboard.db')

def backup_database():
    """备份数据库"""
    db_path = get_db_path()
    if os.path.exists(db_path):
        backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ 数据库已备份到: {backup_path}")
        return backup_path
    return None

def check_column_exists(cursor, table_name, column_name):
    """检查列是否存在"""
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = [row[1] for row in cursor.fetchall()]
    return column_name in columns

def migrate_users_table(cursor):
    """迁移users表，添加新字段"""
    print("🔧 迁移users表...")

    # 检查profession_type字段是否存在
    if not check_column_exists(cursor, 'users', 'profession_type'):
        cursor.execute("ALTER TABLE users ADD COLUMN profession_type VARCHAR(50) DEFAULT 'ai_engineer'")
        print("  ✅ 添加profession_type字段")
    else:
        print("  ⚠️  profession_type字段已存在")

    # 检查profession_config字段是否存在
    if not check_column_exists(cursor, 'users', 'profession_config'):
        cursor.execute("ALTER TABLE users ADD COLUMN profession_config JSON")
        print("  ✅ 添加profession_config字段")
    else:
        print("  ⚠️  profession_config字段已存在")

def migrate_tech_skills_table(cursor):
    """迁移tech_skills表，添加新字段"""
    print("🔧 迁移tech_skills表...")

    # 检查profession_type字段是否存在
    if not check_column_exists(cursor, 'tech_skills', 'profession_type'):
        cursor.execute("ALTER TABLE tech_skills ADD COLUMN profession_type VARCHAR(50) DEFAULT 'ai_engineer'")
        print("  ✅ 添加profession_type字段")
    else:
        print("  ⚠️  profession_type字段已存在")

def migrate_soft_skills_table(cursor):
    """迁移soft_skills表，添加新字段"""
    print("🔧 迁移soft_skills表...")

    # 检查profession_type字段是否存在
    if not check_column_exists(cursor, 'soft_skills', 'profession_type'):
        cursor.execute("ALTER TABLE soft_skills ADD COLUMN profession_type VARCHAR(50) DEFAULT 'ai_engineer'")
        print("  ✅ 添加profession_type字段")
    else:
        print("  ⚠️  profession_type字段已存在")

def create_new_tables(cursor):
    """创建新的表"""
    print("🔧 创建新表...")

    # 创建profession_templates表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS profession_templates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL UNIQUE,
            display_name VARCHAR(100) NOT NULL,
            description TEXT,
            config JSON,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    print("  ✅ 创建profession_templates表")

    # 创建skill_categories表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS skill_categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            profession_template_id INTEGER NOT NULL,
            category_type VARCHAR(20) NOT NULL,
            name VARCHAR(100) NOT NULL,
            display_name VARCHAR(100) NOT NULL,
            description TEXT,
            sort_order INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            FOREIGN KEY (profession_template_id) REFERENCES profession_templates (id)
        )
    """)
    print("  ✅ 创建skill_categories表")

def verify_migration(cursor):
    """验证迁移结果"""
    print("🔍 验证迁移结果...")

    # 检查users表结构
    cursor.execute("PRAGMA table_info(users)")
    users_columns = [row[1] for row in cursor.fetchall()]
    print(f"  📋 users表字段: {', '.join(users_columns)}")

    # 检查tech_skills表结构
    cursor.execute("PRAGMA table_info(tech_skills)")
    tech_skills_columns = [row[1] for row in cursor.fetchall()]
    print(f"  📋 tech_skills表字段: {', '.join(tech_skills_columns)}")

    # 检查新表是否存在
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row[0] for row in cursor.fetchall()]

    required_tables = ['profession_templates', 'skill_categories']
    for table in required_tables:
        if table in tables:
            print(f"  ✅ {table}表已创建")
        else:
            print(f"  ❌ {table}表未创建")

def main():
    """主迁移流程"""
    print("🚀 开始数据库架构迁移...")
    print("=" * 50)

    db_path = get_db_path()

    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        print("请先运行应用程序以创建数据库")
        return

    # 备份数据库
    backup_path = backup_database()

    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 执行迁移
        migrate_users_table(cursor)
        migrate_tech_skills_table(cursor)
        migrate_soft_skills_table(cursor)
        create_new_tables(cursor)

        # 提交更改
        conn.commit()

        # 验证迁移
        verify_migration(cursor)

        # 关闭连接
        cursor.close()
        conn.close()

        print("=" * 50)
        print("🎉 数据库架构迁移完成！")
        print("\n📋 迁移总结:")
        print("✅ users表已添加profession_type和profession_config字段")
        print("✅ tech_skills表已添加profession_type字段")
        print("✅ soft_skills表已添加profession_type字段")
        print("✅ 新表profession_templates和skill_categories已创建")
        print(f"\n💾 数据库备份: {backup_path}")
        print("\n🔄 下一步:")
        print("1. 运行 python migrate_to_universal.py 初始化职业模板数据")
        print("2. 重启应用程序")
        print("3. 测试新功能")

    except Exception as e:
        print(f"❌ 迁移失败: {str(e)}")
        if backup_path and os.path.exists(backup_path):
            print(f"💾 可以从备份恢复: {backup_path}")
        raise

if __name__ == '__main__':
    main()
