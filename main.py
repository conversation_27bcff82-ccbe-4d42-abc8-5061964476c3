from flask import Flask, jsonify
from flask_cors import CORS
import os

def create_app():
    app = Flask(__name__)
    CORS(app)  # 启用CORS支持跨域请求

    # 配置数据库
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///ai_dev_dashboard.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev_key')

    # 初始化数据库
    from user import db
    db.init_app(app)

    # 注册蓝图
    from auth import auth_bp
    from skills import skills_bp
    from assessment_routes import assessment_bp  # 新增

    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(skills_bp, url_prefix='/api/skills')
    app.register_blueprint(assessment_bp, url_prefix='/api/assessment')  # 新增

    # 创建数据库表
    with app.app_context():
        # 导入所有模型以确保表被创建
        from assessment import TechSkill, SoftSkill, Project, LearningActivity, IndustryKnowledge, CareerGoal, EthicsCheck, HealthMetric
        db.create_all()

    @app.route('/')
    def index():
        return jsonify({'message': 'AI应用开发者活动管理系统API服务'})

    @app.route('/api')
    def api_index():
        return jsonify({'message': 'AI应用开发者活动管理系统API服务', 'version': '1.0'})

    return app

app = create_app()

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5002, debug=True)
