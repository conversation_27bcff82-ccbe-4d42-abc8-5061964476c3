#!/usr/bin/env python3
"""
职业模板管理API路由
支持多职业类型的动态配置
"""

from flask import Blueprint, request, jsonify
from auth import token_required
from user import db, User
from profession_templates import PROFESSION_TEMPLATES, get_profession_template, get_all_professions, get_profession_display_names

profession_bp = Blueprint('profession', __name__)

@profession_bp.route('/templates', methods=['GET'])
def get_profession_templates():
    """获取所有职业模板"""
    try:
        templates = []
        for name, config in PROFESSION_TEMPLATES.items():
            templates.append({
                'name': name,
                'display_name': config['display_name'],
                'description': config['description']
            })
        return jsonify({'templates': templates}), 200
    except Exception as e:
        return jsonify({'message': f'获取职业模板失败: {str(e)}'}), 500

@profession_bp.route('/templates/<profession_name>', methods=['GET'])
def get_profession_detail(profession_name):
    """获取指定职业的详细配置"""
    try:
        template = get_profession_template(profession_name)
        if not template:
            return jsonify({'message': '职业模板不存在'}), 404
        return jsonify({'template': template}), 200
    except Exception as e:
        return jsonify({'message': f'获取职业详情失败: {str(e)}'}), 500

@profession_bp.route('/user-profession', methods=['GET'])
@token_required
def get_user_profession(current_user_id):
    """获取用户当前职业类型"""
    try:
        user = User.query.get(current_user_id)
        if not user:
            return jsonify({'message': '用户不存在'}), 404
        
        profession_template = get_profession_template(user.profession_type)
        return jsonify({
            'profession_type': user.profession_type,
            'profession_config': user.profession_config,
            'template': profession_template
        }), 200
    except Exception as e:
        return jsonify({'message': f'获取用户职业信息失败: {str(e)}'}), 500

@profession_bp.route('/user-profession', methods=['PUT'])
@token_required
def update_user_profession(current_user_id):
    """更新用户职业类型"""
    try:
        data = request.get_json()
        profession_type = data.get('profession_type')
        
        if not profession_type:
            return jsonify({'message': '职业类型不能为空'}), 400
        
        if profession_type not in get_all_professions():
            return jsonify({'message': '不支持的职业类型'}), 400
        
        user = User.query.get(current_user_id)
        if not user:
            return jsonify({'message': '用户不存在'}), 404
        
        user.profession_type = profession_type
        user.profession_config = data.get('profession_config', {})
        
        db.session.commit()
        
        return jsonify({'message': '职业类型更新成功'}), 200
    except Exception as e:
        return jsonify({'message': f'更新职业类型失败: {str(e)}'}), 500

@profession_bp.route('/skill-categories/<profession_name>', methods=['GET'])
def get_skill_categories(profession_name):
    """获取指定职业的技能分类"""
    try:
        template = get_profession_template(profession_name)
        if not template:
            return jsonify({'message': '职业模板不存在'}), 404
        
        config = template.get('config', {})
        return jsonify({
            'tech_skill_categories': config.get('tech_skill_categories', []),
            'soft_skills': config.get('soft_skills', []),
            'industries': config.get('industries', []),
            'ethics_checks': config.get('ethics_checks', [])
        }), 200
    except Exception as e:
        return jsonify({'message': f'获取技能分类失败: {str(e)}'}), 500

@profession_bp.route('/initialize-user-data', methods=['POST'])
@token_required
def initialize_user_data(current_user_id):
    """为用户初始化职业相关的示例数据"""
    try:
        data = request.get_json()
        profession_type = data.get('profession_type')
        
        if not profession_type:
            return jsonify({'message': '职业类型不能为空'}), 400
        
        template = get_profession_template(profession_type)
        if not template:
            return jsonify({'message': '职业模板不存在'}), 404
        
        # 这里可以根据职业模板初始化示例数据
        # 具体实现可以调用不同的初始化函数
        
        return jsonify({'message': f'{template["display_name"]}示例数据初始化成功'}), 200
    except Exception as e:
        return jsonify({'message': f'初始化数据失败: {str(e)}'}), 500

@profession_bp.route('/dashboard-config/<profession_name>', methods=['GET'])
def get_dashboard_config(profession_name):
    """获取指定职业的仪表盘配置"""
    try:
        template = get_profession_template(profession_name)
        if not template:
            return jsonify({'message': '职业模板不存在'}), 404
        
        # 根据职业类型返回不同的仪表盘配置
        dashboard_config = {
            'title': f'{template["display_name"]}能力仪表盘',
            'description': f'全面跟踪您的{template["display_name"]}技能发展、项目进度和学习路径',
            'modules': [
                {'name': 'tech_skills', 'display_name': '技术能力', 'enabled': True},
                {'name': 'soft_skills', 'display_name': '软技能', 'enabled': True},
                {'name': 'projects', 'display_name': '项目经验', 'enabled': True},
                {'name': 'learning', 'display_name': '学习活动', 'enabled': True},
                {'name': 'career_goals', 'display_name': '职业目标', 'enabled': True},
                {'name': 'industry_knowledge', 'display_name': '行业知识', 'enabled': True},
                {'name': 'ethics', 'display_name': '伦理合规', 'enabled': True},
                {'name': 'health', 'display_name': '健康动力', 'enabled': True}
            ]
        }
        
        return jsonify({'config': dashboard_config}), 200
    except Exception as e:
        return jsonify({'message': f'获取仪表盘配置失败: {str(e)}'}), 500
