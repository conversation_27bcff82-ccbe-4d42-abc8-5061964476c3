import { useState, useEffect } from 'react';
import Dashboard from './Dashboard';
import TechSkillsPage from './TechSkillsPage';
import ProjectsPage from './ProjectsPage';
import LearningPage from './LearningPage';
import IndustryKnowledgePage from './IndustryKnowledgePage';
import SoftSkillsPage from './SoftSkillsPage';
import CareerGoalsPage from './CareerGoalsPage';
import EthicsPage from './EthicsPage';
import HealthPage from './HealthPage';
import Sidebar from './Sidebar';
import Login from './Login';

const App = () => {
  const [currentPage, setCurrentPage] = useState('/');
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(!!localStorage.getItem('token'));

  useEffect(() => {
    // 初始化时根据当前URL设置页面
    const path = window.location.pathname;
    console.log('初始URL路径:', path);
    setCurrentPage(path);

    // 模拟加载过程
    console.log('App组件正在初始化...');
    setTimeout(() => {
      setIsLoading(false);
      console.log('App组件初始化完成');
    }, 100);

    // 监听浏览器前进后退按钮
    const handlePopState = () => {
      const newPath = window.location.pathname;
      console.log('浏览器导航到:', newPath);
      setCurrentPage(newPath);
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  const handlePageChange = (page: string) => {
    console.log('页面切换到:', page);
    setCurrentPage(page);
    // 更新浏览器URL
    if (window.location.pathname !== page) {
      window.history.pushState({}, '', page);
    }
  };

  const handleLoginSuccess = (redirectPath?: string) => {
    setIsAuthenticated(true);
    setCurrentPage(redirectPath || '/');
    window.history.pushState({}, '', redirectPath || '/');
  };

  if (isLoading) {
    return (
      <div className="flex h-screen bg-gray-50 dark:bg-gray-900 items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">正在加载...</p>
        </div>
      </div>
    );
  }

  const renderPage = () => {
    // 未登录且不是在/login，强制跳转到登录页
    if (!isAuthenticated && currentPage !== '/login') {
      window.history.pushState({}, '', `/login?redirect=${currentPage}`);
      setCurrentPage('/login');
    }
    console.log('当前页面:', currentPage);
    try {
      switch (currentPage) {
        case '/login':
          return <Login onLoginSuccess={handleLoginSuccess} />;
        case '/':
          return <Dashboard />;
        case '/tech-skills':
          return <TechSkillsPage />;
        case '/projects':
          return <ProjectsPage />;
        case '/learning':
          return <LearningPage />;
        case '/industry':
          return <IndustryKnowledgePage />;
        case '/soft-skills':
          return <SoftSkillsPage />;
        case '/career':
          return <CareerGoalsPage />;
        case '/ethics':
          return <EthicsPage />;
        case '/health':
          return <HealthPage />;
        // 暂时对其他页面返回Dashboard
        case '/profile':
        case '/settings':
          return (
            <div className="flex flex-col flex-1 overflow-hidden">
              <div className="flex-1 overflow-y-auto p-4 md:p-6">
                <div className="text-center py-12">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                    功能开发中
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    该功能正在开发中，敬请期待！
                  </p>
                  <button
                    onClick={() => handlePageChange('/')}
                    className="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
                  >
                    返回仪表盘
                  </button>
                </div>
              </div>
            </div>
          );
        default:
          return <Dashboard />;
      }
    } catch (error) {
      console.error('页面渲染错误:', error);
      return (
        <div className="flex flex-col flex-1 overflow-hidden">
          <div className="flex-1 overflow-y-auto p-4 md:p-6">
            <div className="text-center py-12">
              <h2 className="text-2xl font-bold text-red-600 mb-4">页面加载错误</h2>
              <p className="text-gray-600 mb-4">页面加载时出现错误，请刷新页面重试。</p>
              <button
                onClick={() => handlePageChange('/')}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
              >
                返回仪表盘
              </button>
            </div>
          </div>
        </div>
      );
    }
  };

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <Sidebar currentPage={currentPage} onPageChange={handlePageChange} />
      {renderPage()}
    </div>
  );
};

export default App;