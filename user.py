from flask_sqlalchemy import SQLAlchemy
from datetime import datetime

db = SQLAlchemy()

class User(db.Model):
    """用户模型 - 通用化改造"""
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    profession_type = db.Column(db.String(50), default='ai_engineer')  # 职业类型
    profession_config = db.Column(db.JSON)  # 个性化职业配置
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联关系
    tech_skills = db.relationship('TechSkill', backref='user', lazy=True)
    soft_skills = db.relationship('SoftSkill', backref='user', lazy=True)
    projects = db.relationship('Project', backref='user', lazy=True)
    learning_activities = db.relationship('LearningActivity', backref='user', lazy=True)
    industry_knowledge = db.relationship('IndustryKnowledge', backref='user', lazy=True)
    career_goals = db.relationship('CareerGoal', backref='user', lazy=True)
    ethics_checks = db.relationship('EthicsCheck', backref='user', lazy=True)
    health_metrics = db.relationship('HealthMetric', backref='user', lazy=True)

    def __repr__(self):
        return f'<User {self.username}>'
